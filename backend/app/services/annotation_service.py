"""
标注管理服务类
"""

from typing import Any, Dict, List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_, text
from datetime import datetime

from app.models.annotation import AnnotationTask, AnnotationLog, AnnotationReview, TaskStatus, ReviewStatus
from app.models.mapping import ItemKpMap, QuestionRelation
from app.models.question import Question
from app.models.knowledge import KnowledgePoint
from app.schemas.annotation import (
    ItemKpMapCreate, ItemKpMapUpdate, AnnotationTaskCreate, AnnotationTaskUpdate,
    AnnotationLogCreate, BatchAnnotationRequest, AnnotationReviewCreate,
    AnnotationReviewUpdate, TaskWorkflowAction
)


class AnnotationService:
    """标注服务类"""
    
    def __init__(self, db: Session):
        self.db = db

    def get(self, task_id: int) -> Optional[AnnotationTask]:
        """根据ID获取标注任务"""
        return self.db.query(AnnotationTask).filter(AnnotationTask.task_id == task_id).first()

    def get_task(self, task_id: int) -> Optional[AnnotationTask]:
        """根据ID获取标注任务（别名方法）"""
        return self.get(task_id)
    
    def create_mapping(self, mapping_in: ItemKpMapCreate, creator_id: int) -> ItemKpMap:
        """创建题目-知识点映射"""
        # 检查是否已存在
        existing = self.db.query(ItemKpMap).filter(
            ItemKpMap.question_id == mapping_in.question_id,
            ItemKpMap.kp_id == mapping_in.kp_id
        ).first()
        
        if existing:
            raise ValueError("映射关系已存在")
        
        mapping_data = mapping_in.model_dump()
        mapping_data['created_by'] = creator_id
        mapping_data['updated_by'] = creator_id
        
        mapping = ItemKpMap(**mapping_data)
        self.db.add(mapping)
        self.db.commit()
        self.db.refresh(mapping)
        
        # 记录日志
        self._log_annotation(
            task_id=None,
            question_id=mapping_in.question_id,
            action="create_mapping",
            new_value=mapping_data,
            operator_id=creator_id
        )
        
        return mapping
    
    def update_mapping(
        self, 
        question_id: int, 
        kp_id: int, 
        mapping_in: ItemKpMapUpdate,
        operator_id: int
    ) -> ItemKpMap:
        """更新题目-知识点映射"""
        mapping = self.db.query(ItemKpMap).filter(
            ItemKpMap.question_id == question_id,
            ItemKpMap.kp_id == kp_id
        ).first()
        
        if not mapping:
            raise ValueError("映射关系不存在")
        
        # 记录旧值
        old_value = {
            'is_required': mapping.is_required,
            'weight': mapping.weight,
            'confidence': mapping.confidence,
            'source': mapping.source
        }
        
        update_data = mapping_in.model_dump(exclude_unset=True)
        update_data['updated_by'] = operator_id
        
        for field, value in update_data.items():
            setattr(mapping, field, value)
        
        self.db.commit()
        self.db.refresh(mapping)
        
        # 记录日志
        self._log_annotation(
            task_id=None,
            question_id=question_id,
            action="update_mapping",
            old_value=old_value,
            new_value=update_data,
            operator_id=operator_id
        )
        
        return mapping
    
    def remove_mapping(self, question_id: int, kp_id: int, operator_id: int) -> bool:
        """删除题目-知识点映射"""
        mapping = self.db.query(ItemKpMap).filter(
            ItemKpMap.question_id == question_id,
            ItemKpMap.kp_id == kp_id
        ).first()
        
        if mapping:
            # 记录旧值
            old_value = {
                'is_required': mapping.is_required,
                'weight': mapping.weight,
                'confidence': mapping.confidence,
                'source': mapping.source
            }
            
            self.db.delete(mapping)
            self.db.commit()
            
            # 记录日志
            self._log_annotation(
                task_id=None,
                question_id=question_id,
                action="remove_mapping",
                old_value=old_value,
                operator_id=operator_id
            )
            
            return True
        return False
    
    def get_mappings(
        self, 
        question_id: Optional[int] = None,
        kp_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[ItemKpMap]:
        """获取映射关系"""
        query = self.db.query(ItemKpMap).options(
            joinedload(ItemKpMap.question),
            joinedload(ItemKpMap.knowledge_point)
        )
        
        if question_id:
            query = query.filter(ItemKpMap.question_id == question_id)
        
        if kp_id:
            query = query.filter(ItemKpMap.kp_id == kp_id)
        
        return query.offset(skip).limit(limit).all()
    
    def batch_annotate(self, request: BatchAnnotationRequest, creator_id: int) -> Dict[str, Any]:
        """批量标注"""
        success_count = 0
        error_count = 0
        errors = []
        
        for question_id in request.question_ids:
            try:
                # 检查题目是否存在
                question = self.db.query(Question).filter(
                    Question.question_id == question_id
                ).first()
                
                if not question:
                    error_count += 1
                    errors.append(f"题目 {question_id} 不存在")
                    continue
                
                # 检查知识点是否存在
                kp = self.db.query(KnowledgePoint).filter(
                    KnowledgePoint.kp_id == request.kp_id
                ).first()
                
                if not kp:
                    error_count += 1
                    errors.append(f"知识点 {request.kp_id} 不存在")
                    continue
                
                # 创建映射
                mapping_in = ItemKpMapCreate(
                    question_id=question_id,
                    kp_id=request.kp_id,
                    is_required=request.is_required,
                    weight=request.weight,
                    confidence=request.confidence,
                    source=request.source
                )
                
                self.create_mapping(mapping_in, creator_id)
                success_count += 1
                
            except Exception as e:
                error_count += 1
                errors.append(f"题目 {question_id} 标注失败: {str(e)}")
        
        return {
            'success_count': success_count,
            'error_count': error_count,
            'errors': errors
        }
    
    def get_q_matrix(self) -> Dict[str, Any]:
        """获取Q矩阵"""
        # 获取所有题目和知识点
        questions = self.db.query(Question).filter(Question.is_active == True).all()
        knowledge_points = self.db.query(KnowledgePoint).all()
        
        # 获取所有映射关系
        mappings = self.db.query(ItemKpMap).all()
        
        # 构建映射字典
        mapping_dict = {}
        for mapping in mappings:
            key = (mapping.question_id, mapping.kp_id)
            mapping_dict[key] = mapping.weight if mapping.is_required else 0
        
        # 构建矩阵
        matrix = []
        for question in questions:
            row = []
            for kp in knowledge_points:
                key = (question.question_id, kp.kp_id)
                value = mapping_dict.get(key, 0)
                row.append(value)
            matrix.append(row)
        
        return {
            'questions': questions,
            'knowledge_points': knowledge_points,
            'matrix': matrix,
            'metadata': {
                'question_count': len(questions),
                'kp_count': len(knowledge_points),
                'mapping_count': len(mappings),
                'generated_at': datetime.now().isoformat()
            }
        }
    
    def create_task(self, task_in: AnnotationTaskCreate, creator_id: int) -> AnnotationTask:
        """创建标注任务"""
        task_data = task_in.model_dump(exclude={'assignees', 'question_ids'})
        task_data['created_by'] = creator_id
        task_data['updated_by'] = creator_id
        task_data['status'] = 0  # PENDING
        task_data['progress'] = 0.0
        
        task = AnnotationTask(**task_data)
        self.db.add(task)
        self.db.flush()  # 获取ID
        
        # 处理分配用户和题目（这里简化处理，实际需要更复杂的逻辑）
        
        self.db.commit()
        self.db.refresh(task)
        return task
    
    def update_task(self, task_id: int, task_in: AnnotationTaskUpdate, updater_id: Optional[int] = None) -> AnnotationTask:
        """更新标注任务"""
        task = self.db.query(AnnotationTask).filter(
            AnnotationTask.task_id == task_id
        ).first()

        if not task:
            raise ValueError("任务不存在")

        update_data = task_in.model_dump(exclude_unset=True, exclude={'assignees'})

        for field, value in update_data.items():
            setattr(task, field, value)

        if updater_id:
            task.updated_by = updater_id

        self.db.commit()
        self.db.refresh(task)
        return task
    
    def get_task_progress(self, task_id: int) -> Dict[str, Any]:
        """获取任务进度"""
        task = self.db.query(AnnotationTask).filter(
            AnnotationTask.task_id == task_id
        ).first()
        
        if not task:
            raise ValueError("任务不存在")
        
        # 这里简化处理，实际需要根据具体的任务类型计算进度
        total_questions = 0
        completed_questions = 0
        
        progress = completed_questions / total_questions if total_questions > 0 else 0
        
        return {
            'task_id': task_id,
            'progress': progress,
            'total_questions': total_questions,
            'completed_questions': completed_questions,
            'status': task.status
        }
    
    def _log_annotation(
        self,
        task_id: Optional[int],
        question_id: int,
        action: str,
        operator_id: int,
        old_value: Optional[Dict[str, Any]] = None,
        new_value: Optional[Dict[str, Any]] = None,
        comment: Optional[str] = None
    ):
        """记录标注日志"""
        log = AnnotationLog(
            task_id=task_id,
            question_id=question_id,
            action=action,
            old_value=old_value,
            new_value=new_value,
            comment=comment,
            operator_id=operator_id
        )
        
        self.db.add(log)
        # 不在这里提交，由调用方决定何时提交
    
    def get_annotation_stats(self) -> Dict[str, Any]:
        """获取标注统计信息"""
        total_questions = self.db.query(func.count(Question.question_id)).filter(
            Question.is_active == True
        ).scalar()
        
        annotated_questions = self.db.query(func.count(func.distinct(ItemKpMap.question_id))).scalar()
        
        total_mappings = self.db.query(func.count(ItemKpMap.question_id)).scalar()
        
        avg_mappings = total_mappings / annotated_questions if annotated_questions > 0 else 0
        coverage = annotated_questions / total_questions if total_questions > 0 else 0
        
        return {
            'total_questions': total_questions,
            'annotated_questions': annotated_questions,
            'total_mappings': total_mappings,
            'avg_mappings_per_question': avg_mappings,
            'annotation_coverage': coverage
        }

    # 审核相关方法
    def submit_task_for_review(self, task_id: int, submitter_id: int) -> AnnotationTask:
        """提交任务进行审核"""
        task = self.db.query(AnnotationTask).filter(
            AnnotationTask.task_id == task_id
        ).first()

        if not task:
            raise ValueError("任务不存在")

        if task.status != TaskStatus.COMPLETED.value:
            raise ValueError("只有已完成的任务才能提交审核")

        # 更新任务状态
        task.status = TaskStatus.UNDER_REVIEW.value
        task.updated_by = submitter_id
        task.updated_at = datetime.now()

        # 记录操作日志
        log = AnnotationLog(
            task_id=task_id,
            user_id=submitter_id,
            action="submit_for_review",
            comment="提交任务进行审核"
        )
        self.db.add(log)

        self.db.commit()
        self.db.refresh(task)
        return task

    def assign_reviewer(self, task_id: int, reviewer_id: int, assigner_id: int) -> AnnotationTask:
        """分配审核员"""
        task = self.db.query(AnnotationTask).filter(
            AnnotationTask.task_id == task_id
        ).first()

        if not task:
            raise ValueError("任务不存在")

        if task.status != TaskStatus.UNDER_REVIEW.value:
            raise ValueError("只有审核中的任务才能分配审核员")

        # 更新审核员
        task.reviewer_id = reviewer_id
        task.updated_by = assigner_id
        task.updated_at = datetime.now()

        # 记录操作日志
        log = AnnotationLog(
            task_id=task_id,
            user_id=assigner_id,
            action="assign_reviewer",
            comment=f"分配审核员: {reviewer_id}"
        )
        self.db.add(log)

        self.db.commit()
        self.db.refresh(task)
        return task

    def create_review(self, review_in: AnnotationReviewCreate, reviewer_id: int) -> AnnotationReview:
        """创建审核记录"""
        # 检查任务是否存在且处于审核状态
        task = self.db.query(AnnotationTask).filter(
            AnnotationTask.task_id == review_in.task_id
        ).first()

        if not task:
            raise ValueError("任务不存在")

        if task.status != TaskStatus.UNDER_REVIEW.value:
            raise ValueError("任务不在审核状态")

        if task.reviewer_id != reviewer_id:
            raise ValueError("只有指定的审核员才能审核此任务")

        # 创建审核记录
        review_data = review_in.model_dump()
        review_data['reviewer_id'] = reviewer_id
        review_data['created_by'] = reviewer_id
        review_data['updated_by'] = reviewer_id

        review = AnnotationReview(**review_data)
        self.db.add(review)

        # 更新任务状态
        if review_in.review_status == ReviewStatus.APPROVED.value:
            task.status = TaskStatus.REVIEWED.value
        elif review_in.review_status == ReviewStatus.REJECTED.value:
            task.status = TaskStatus.REJECTED.value
        elif review_in.review_status == ReviewStatus.NEEDS_REVISION.value:
            task.status = TaskStatus.IN_PROGRESS.value

        task.reviewed_at = datetime.now()
        task.updated_by = reviewer_id
        task.updated_at = datetime.now()

        # 记录操作日志
        log = AnnotationLog(
            task_id=review_in.task_id,
            user_id=reviewer_id,
            action="review_task",
            comment=f"审核结果: {review_in.review_status}"
        )
        self.db.add(log)

        self.db.commit()
        self.db.refresh(review)
        return review

    def get_reviews_by_task(self, task_id: int) -> List[AnnotationReview]:
        """获取任务的所有审核记录"""
        return self.db.query(AnnotationReview).filter(
            AnnotationReview.task_id == task_id
        ).order_by(AnnotationReview.created_at.desc()).all()

    def get_pending_review_tasks(self, reviewer_id: Optional[int] = None, skip: int = 0, limit: int = 100) -> List[AnnotationTask]:
        """获取待审核的任务列表"""
        query = self.db.query(AnnotationTask).filter(
            AnnotationTask.status == TaskStatus.UNDER_REVIEW.value
        )

        if reviewer_id:
            query = query.filter(AnnotationTask.reviewer_id == reviewer_id)

        return query.offset(skip).limit(limit).all()

    def execute_workflow_action(self, task_id: int, action: TaskWorkflowAction, operator_id: int) -> AnnotationTask:
        """执行工作流操作"""
        task = self.db.query(AnnotationTask).filter(
            AnnotationTask.task_id == task_id
        ).first()

        if not task:
            raise ValueError("任务不存在")

        # 根据操作类型执行相应的状态转换
        if action.action == "start":
            if task.status != TaskStatus.PENDING.value:
                raise ValueError("只有待处理的任务才能开始")
            task.status = TaskStatus.IN_PROGRESS.value
            task.started_at = datetime.now()

        elif action.action == "submit":
            if task.status != TaskStatus.IN_PROGRESS.value:
                raise ValueError("只有进行中的任务才能提交")
            task.status = TaskStatus.COMPLETED.value
            task.completed_at = datetime.now()

        elif action.action == "approve":
            if task.status != TaskStatus.UNDER_REVIEW.value:
                raise ValueError("只有审核中的任务才能批准")
            if task.reviewer_id != operator_id:
                raise ValueError("只有指定的审核员才能批准任务")
            task.status = TaskStatus.REVIEWED.value
            task.reviewed_at = datetime.now()

        elif action.action == "reject":
            if task.status != TaskStatus.UNDER_REVIEW.value:
                raise ValueError("只有审核中的任务才能拒绝")
            if task.reviewer_id != operator_id:
                raise ValueError("只有指定的审核员才能拒绝任务")
            task.status = TaskStatus.REJECTED.value
            task.reviewed_at = datetime.now()

        elif action.action == "request_revision":
            if task.status != TaskStatus.UNDER_REVIEW.value:
                raise ValueError("只有审核中的任务才能要求修改")
            if task.reviewer_id != operator_id:
                raise ValueError("只有指定的审核员才能要求修改")
            task.status = TaskStatus.IN_PROGRESS.value

        elif action.action == "cancel":
            if task.status in [TaskStatus.REVIEWED.value, TaskStatus.CANCELLED.value]:
                raise ValueError("已完成或已取消的任务不能取消")
            task.status = TaskStatus.CANCELLED.value

        else:
            raise ValueError(f"不支持的操作: {action.action}")

        # 更新任务
        task.updated_by = operator_id
        task.updated_at = datetime.now()

        # 记录操作日志
        log = AnnotationLog(
            task_id=task_id,
            user_id=operator_id,
            action=action.action,
            comment=action.comment or f"执行操作: {action.action}"
        )
        self.db.add(log)

        self.db.commit()
        self.db.refresh(task)
        return task

    def calculate_task_progress(self, task_id: int) -> Dict[str, Any]:
        """计算任务进度"""
        task = self.db.query(AnnotationTask).filter(
            AnnotationTask.task_id == task_id
        ).first()

        if not task:
            raise ValueError("任务不存在")

        # 根据任务类型计算进度
        if task.task_type == 0:  # 题目-知识点映射
            # 获取任务相关的题目数量和已标注数量
            # 这里简化处理，实际需要根据任务数据计算
            total_items = 100  # 示例数据
            completed_items = int(task.progress)
            progress = completed_items / total_items if total_items > 0 else 0
        else:
            # 其他类型的任务进度计算
            total_items = 100
            completed_items = int(task.progress)
            progress = completed_items / total_items if total_items > 0 else 0

        return {
            'task_id': task_id,
            'progress': progress,
            'total_items': total_items,
            'completed_items': completed_items,
            'status': task.status
        }

    def get_tasks(self, **kwargs) -> List[AnnotationTask]:
        """获取任务列表"""
        query = self.db.query(AnnotationTask)

        # 应用过滤条件
        if 'assignee_id' in kwargs:
            query = query.filter(AnnotationTask.assigned_to == kwargs['assignee_id'])
        if 'status' in kwargs:
            query = query.filter(AnnotationTask.status == kwargs['status'])
        if 'task_type' in kwargs:
            query = query.filter(AnnotationTask.task_type == kwargs['task_type'])

        return query.all()

    def assign_task(self, task_id: int, assignee_id: int) -> Optional[AnnotationTask]:
        """分配任务"""
        task = self.get(task_id)
        if task:
            task.assigned_to = assignee_id
            task.status = 1  # ASSIGNED
            self.db.commit()
            self.db.refresh(task)
        return task

    def bulk_assign_tasks(self, task_ids: List[int], assignee_id: int) -> List[AnnotationTask]:
        """批量分配任务"""
        tasks = []
        for task_id in task_ids:
            task = self.assign_task(task_id, assignee_id)
            if task:
                tasks.append(task)
        return tasks

    def add_task_log(self, task_id: int, user_id: int, action: int, description: str, metadata: Optional[Dict[str, Any]] = None) -> AnnotationLog:
        """添加任务日志"""
        log = AnnotationLog(
            task_id=task_id,
            user_id=user_id,
            action=action,
            description=description
        )
        self.db.add(log)
        self.db.commit()
        self.db.refresh(log)
        return log

    def get_tasks_by_status(self, status) -> List[AnnotationTask]:
        """根据状态获取任务"""
        # 支持字符串状态参数
        if isinstance(status, str):
            status_map = {
                "pending": 0,
                "in_progress": 1,
                "completed": 2,
                "under_review": 3,
                "reviewed": 4,
                "rejected": 5,
                "cancelled": 6
            }
            status = status_map.get(status.lower(), status)

        return self.get_tasks(status=status)

    def get_tasks_by_type(self, task_type: int) -> List[AnnotationTask]:
        """根据类型获取任务"""
        return self.get_tasks(task_type=task_type)

    def start_task(self, task_id: int, user_id: int) -> Optional[AnnotationTask]:
        """开始任务"""
        task = self.get(task_id)
        if task:
            task.status = 2  # IN_PROGRESS
            task.assigned_to = user_id
            self.db.commit()
            self.db.refresh(task)

            # 添加日志
            self.add_task_log(task_id, user_id, 2, "开始任务")
        return task

    def complete_task(self, task_id: int, user_id: int) -> Optional[AnnotationTask]:
        """完成任务"""
        task = self.get(task_id)
        if task:
            task.status = 3  # COMPLETED
            task.progress = 100.0
            self.db.commit()
            self.db.refresh(task)

            # 添加日志
            self.add_task_log(task_id, user_id, 3, "完成任务")
        return task

    def cancel_task(self, task_id: int, user_id: int) -> Optional[AnnotationTask]:
        """取消任务"""
        task = self.get(task_id)
        if task:
            task.status = 4  # CANCELLED
            self.db.commit()
            self.db.refresh(task)

            # 添加日志
            self.add_task_log(task_id, user_id, 4, "取消任务")
        return task

    def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        total_tasks = self.db.query(func.count(AnnotationTask.task_id)).scalar()
        pending_tasks = self.db.query(func.count(AnnotationTask.task_id)).filter(
            AnnotationTask.status == 0
        ).scalar()
        in_progress_tasks = self.db.query(func.count(AnnotationTask.task_id)).filter(
            AnnotationTask.status == 2
        ).scalar()
        completed_tasks = self.db.query(func.count(AnnotationTask.task_id)).filter(
            AnnotationTask.status == 3
        ).scalar()

        return {
            'total_tasks': total_tasks,
            'pending_tasks': pending_tasks,
            'in_progress_tasks': in_progress_tasks,
            'completed_tasks': completed_tasks
        }

    def get_user_task_statistics(self, user_id: int) -> Dict[str, Any]:
        """获取用户任务统计信息"""
        total_tasks = self.db.query(func.count(AnnotationTask.task_id)).filter(
            AnnotationTask.assigned_to == user_id
        ).scalar()
        completed_tasks = self.db.query(func.count(AnnotationTask.task_id)).filter(
            and_(AnnotationTask.assigned_to == user_id, AnnotationTask.status == 3)
        ).scalar()

        return {
            'total_tasks': total_tasks,
            'completed_tasks': completed_tasks,
            'completion_rate': completed_tasks / total_tasks if total_tasks > 0 else 0
        }

    def get_overdue_tasks(self) -> List[AnnotationTask]:
        """获取过期任务"""
        from datetime import datetime
        return self.db.query(AnnotationTask).filter(
            and_(
                AnnotationTask.due_date < datetime.utcnow(),
                AnnotationTask.status.in_([0, 1, 2])  # PENDING, ASSIGNED, IN_PROGRESS
            )
        ).all()

    def get_tasks_by_assignee(self, assignee_id: int) -> List[AnnotationTask]:
        """根据分配者获取任务"""
        return self.db.query(AnnotationTask).filter(
            AnnotationTask.assigned_to == assignee_id
        ).all()

    def get_tasks_by_reviewer(self, reviewer_id: int) -> List[AnnotationTask]:
        """根据审核者获取任务"""
        return self.db.query(AnnotationTask).filter(
            AnnotationTask.reviewer_id == reviewer_id
        ).all()

    def get_tasks_by_creator(self, creator_id: int) -> List[AnnotationTask]:
        """根据创建者获取任务"""
        return self.db.query(AnnotationTask).filter(
            AnnotationTask.created_by == creator_id
        ).all()

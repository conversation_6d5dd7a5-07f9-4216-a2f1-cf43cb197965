"""
版本管理服务类
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_

from app.models.version import QuestionVersion, KpVersion, ChangeType
from app.models.question import Question
from app.models.knowledge import KnowledgePoint
from app.schemas.version import (
    QuestionVersionCreate, QuestionVersionUpdate, QuestionVersionResponse,
    KpVersionCreate, KpVersionUpdate, KpVersionResponse,
    VersionCompareResponse, VersionRollbackResponse, VersionPublishResponse,
    VersionStatsResponse
)


class VersionService:
    """版本管理服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_question_version(
        self,
        version_in: QuestionVersionCreate,
        creator_id: int
    ) -> QuestionVersionResponse:
        """创建题目版本"""
        # 获取当前最大版本号
        max_version = self.db.query(func.max(QuestionVersion.version_number)).filter(
            QuestionVersion.question_id == version_in.question_id
        ).scalar() or 0

        # 如果是创建操作，将之前的版本设为非当前版本
        if version_in.change_type == ChangeType.CREATE:
            self.db.query(QuestionVersion).filter(
                QuestionVersion.question_id == version_in.question_id,
                QuestionVersion.is_current == True
            ).update({"is_current": False})

        # 创建新版本 - 不包含version_id，让数据库自动生成
        version_data = version_in.model_dump(exclude={'version_id'})
        version_data.update({
            "version_number": max_version + 1,
            "is_current": True,
            "is_published": False,
            "created_by": creator_id,
            "updated_by": creator_id
        })

        version = QuestionVersion(**version_data)
        self.db.add(version)
        self.db.flush()  # 使用flush而不是commit，获取ID
        self.db.refresh(version)
        self.db.commit()

        return QuestionVersionResponse.model_validate(version)
    
    def create_kp_version(
        self,
        version_in: KpVersionCreate,
        creator_id: int
    ) -> KpVersionResponse:
        """创建知识点版本"""
        # 获取当前最大版本号
        max_version = self.db.query(func.max(KpVersion.version_number)).filter(
            KpVersion.kp_id == version_in.kp_id
        ).scalar() or 0

        # 如果是创建操作，将之前的版本设为非当前版本
        if version_in.change_type == ChangeType.CREATE:
            self.db.query(KpVersion).filter(
                KpVersion.kp_id == version_in.kp_id,
                KpVersion.is_current == True
            ).update({"is_current": False})

        # 创建新版本 - 不包含version_id，让数据库自动生成
        version_data = version_in.model_dump(exclude={'version_id'})
        version_data.update({
            "version_number": max_version + 1,
            "is_current": True,
            "is_published": False,
            "created_by": creator_id,
            "updated_by": creator_id
        })

        version = KpVersion(**version_data)
        self.db.add(version)
        self.db.flush()  # 使用flush而不是commit，获取ID
        self.db.refresh(version)
        self.db.commit()

        return KpVersionResponse.model_validate(version)
    
    def get_question_versions(
        self, 
        question_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[QuestionVersionResponse]:
        """获取题目版本列表"""
        versions = self.db.query(QuestionVersion).filter(
            QuestionVersion.question_id == question_id
        ).order_by(desc(QuestionVersion.version_number)).offset(skip).limit(limit).all()
        
        return [QuestionVersionResponse.model_validate(v) for v in versions]
    
    def get_kp_versions(
        self, 
        kp_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[KpVersionResponse]:
        """获取知识点版本列表"""
        versions = self.db.query(KpVersion).filter(
            KpVersion.kp_id == kp_id
        ).order_by(desc(KpVersion.version_number)).offset(skip).limit(limit).all()
        
        return [KpVersionResponse.model_validate(v) for v in versions]
    
    def get_version_by_id(
        self, 
        version_id: int, 
        version_type: str = "question"
    ) -> Union[QuestionVersionResponse, KpVersionResponse, None]:
        """根据ID获取版本"""
        if version_type == "question":
            version = self.db.query(QuestionVersion).filter(
                QuestionVersion.version_id == version_id
            ).first()
            return QuestionVersionResponse.model_validate(version) if version else None
        else:
            version = self.db.query(KpVersion).filter(
                KpVersion.version_id == version_id
            ).first()
            return KpVersionResponse.model_validate(version) if version else None
    
    def compare_versions(
        self, 
        source_version_id: int, 
        target_version_id: int,
        version_type: str = "question"
    ) -> VersionCompareResponse:
        """比较两个版本"""
        source_version = self.get_version_by_id(source_version_id, version_type)
        target_version = self.get_version_by_id(target_version_id, version_type)
        
        if not source_version or not target_version:
            raise ValueError("版本不存在")
        
        # 计算差异
        differences = self._calculate_differences(
            source_version.snapshot_data,
            target_version.snapshot_data
        )
        
        return VersionCompareResponse(
            source_version=source_version,
            target_version=target_version,
            differences=differences
        )
    
    def rollback_to_version(
        self, 
        target_version_id: int, 
        rollback_reason: str,
        operator_id: int,
        version_type: str = "question"
    ) -> VersionRollbackResponse:
        """回滚到指定版本"""
        target_version = self.get_version_by_id(target_version_id, version_type)
        if not target_version:
            raise ValueError("目标版本不存在")
        
        # 创建回滚版本
        if version_type == "question":
            rollback_data = QuestionVersionCreate(
                question_id=target_version.question_id,
                change_type=ChangeType.RESTORE,
                change_description=f"回滚到版本 {target_version.version_number}: {rollback_reason}",
                snapshot_data=target_version.snapshot_data,
                diff_data={"rollback_from": target_version_id}
            )
            new_version = self.create_question_version(rollback_data, operator_id)
        else:
            rollback_data = KpVersionCreate(
                kp_id=target_version.kp_id,
                change_type=ChangeType.RESTORE,
                change_description=f"回滚到版本 {target_version.version_number}: {rollback_reason}",
                snapshot_data=target_version.snapshot_data,
                diff_data={"rollback_from": target_version_id}
            )
            new_version = self.create_kp_version(rollback_data, operator_id)
        
        return VersionRollbackResponse(
            success=True,
            new_version=new_version,
            message=f"成功回滚到版本 {target_version.version_number}"
        )
    
    def publish_version(
        self, 
        version_id: int, 
        publish_note: Optional[str],
        operator_id: int,
        version_type: str = "question"
    ) -> VersionPublishResponse:
        """发布版本"""
        if version_type == "question":
            version = self.db.query(QuestionVersion).filter(
                QuestionVersion.version_id == version_id
            ).first()
        else:
            version = self.db.query(KpVersion).filter(
                KpVersion.version_id == version_id
            ).first()
        
        if not version:
            raise ValueError("版本不存在")
        
        if version.is_published:
            return VersionPublishResponse(
                success=False,
                version=self.get_version_by_id(version_id, version_type),
                message="版本已经发布"
            )
        
        # 更新版本状态
        version.is_published = True
        version.updated_by = operator_id
        if publish_note:
            version.change_description = f"{version.change_description or ''}\n发布说明: {publish_note}"
        
        self.db.commit()
        self.db.refresh(version)
        
        return VersionPublishResponse(
            success=True,
            version=self.get_version_by_id(version_id, version_type),
            message="版本发布成功"
        )
    
    def get_version_stats(self) -> VersionStatsResponse:
        """获取版本统计信息"""
        # 题目版本统计
        question_total = self.db.query(func.count(QuestionVersion.version_id)).scalar()
        question_published = self.db.query(func.count(QuestionVersion.version_id)).filter(
            QuestionVersion.is_published == True
        ).scalar()
        
        # 知识点版本统计
        kp_total = self.db.query(func.count(KpVersion.version_id)).scalar()
        kp_published = self.db.query(func.count(KpVersion.version_id)).filter(
            KpVersion.is_published == True
        ).scalar()
        
        # 变更类型统计
        change_type_stats = {}
        for change_type in ChangeType:
            question_count = self.db.query(func.count(QuestionVersion.version_id)).filter(
                QuestionVersion.change_type == change_type.value
            ).scalar()
            kp_count = self.db.query(func.count(KpVersion.version_id)).filter(
                KpVersion.change_type == change_type.value
            ).scalar()
            change_type_stats[change_type.name] = question_count + kp_count
        
        # 最近7天的变更数
        recent_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        recent_question_changes = self.db.query(func.count(QuestionVersion.version_id)).filter(
            QuestionVersion.created_at >= recent_date
        ).scalar()
        recent_kp_changes = self.db.query(func.count(KpVersion.version_id)).filter(
            KpVersion.created_at >= recent_date
        ).scalar()
        
        return VersionStatsResponse(
            total_versions=question_total + kp_total,
            published_versions=question_published + kp_published,
            draft_versions=(question_total - question_published) + (kp_total - kp_published),
            recent_changes=recent_question_changes + recent_kp_changes,
            change_type_stats=change_type_stats
        )
    
    def _calculate_differences(self, source_data: Dict[str, Any], target_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算两个数据快照的差异"""
        differences = {
            "added": {},
            "removed": {},
            "modified": {}
        }
        
        # 找出新增的字段
        for key in target_data:
            if key not in source_data:
                differences["added"][key] = target_data[key]
        
        # 找出删除的字段
        for key in source_data:
            if key not in target_data:
                differences["removed"][key] = source_data[key]
        
        # 找出修改的字段
        for key in source_data:
            if key in target_data and source_data[key] != target_data[key]:
                differences["modified"][key] = {
                    "old": source_data[key],
                    "new": target_data[key]
                }
        
        return differences

"""
认证相关API端点
"""

from datetime import timedelta
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.database import get_db
from app.core.security import (
    create_access_token,
    create_refresh_token,
    verify_token,
    verify_password
)
from app.api.deps import get_current_user
from app.schemas.auth import Token, TokenRefresh, TokenResponse
from app.schemas.user import UserCreate, UserResponse
from app.models.user import User
from app.services.user_service import UserService

router = APIRouter()


@router.post("/login", response_model=Token)
async def login(
    db: Session = Depends(get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    用户登录
    """
    user_service = UserService(db)
    
    # 验证用户凭据
    user = user_service.authenticate(
        username=form_data.username,
        password=form_data.password
    )
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="账户已被停用"
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=user.user_id,
        expires_delta=access_token_expires
    )

    # 创建刷新令牌
    refresh_token = create_refresh_token(subject=user.user_id)
    
    # 更新最后登录时间
    user_service.update_last_login(user.user_id)
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "user": user
    }


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    token_data: TokenRefresh,
    db: Session = Depends(get_db)
) -> Any:
    """
    刷新访问令牌
    """
    # 验证刷新令牌
    user_id = verify_token(token_data.refresh_token, token_type="refresh")

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新令牌"
        )

    user_service = UserService(db)
    user = user_service.get(int(user_id))

    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在或已被禁用"
        )

    # 创建新的访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=user.user_id,
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_in: UserCreate,
    db: Session = Depends(get_db)
) -> Any:
    """
    用户注册
    """
    user_service = UserService(db)
    
    # 检查用户名是否已存在
    if user_service.get_by_username(user_in.username):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    if user_service.get_by_email(user_in.email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )
    
    # 创建用户
    user = user_service.create(user_in)
    
    return user


@router.post("/logout")
async def logout() -> Any:
    """
    用户登出
    注意：由于使用JWT，实际的登出需要在客户端删除令牌
    这里主要用于记录日志
    """
    return {"message": "成功登出"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    获取当前用户信息
    """
    # 如果current_user是MockUser，需要从数据库获取真实用户
    if hasattr(current_user, 'user_id'):
        user_service = UserService(db)
        real_user = user_service.get(current_user.user_id)
        if real_user:
            return real_user

    # 如果找不到真实用户，返回MockUser的数据
    from datetime import datetime
    return {
        "user_id": current_user.user_id,
        "email": getattr(current_user, 'email', f"user{current_user.user_id}@example.com"),
        "username": getattr(current_user, 'username', f"user{current_user.user_id}"),
        "full_name": getattr(current_user, 'full_name', f"User {current_user.user_id}"),
        "is_active": getattr(current_user, 'is_active', True),
        "role": getattr(current_user, 'role', "annotator"),
        "last_login": None,
        "login_count": 0,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    }


@router.post("/change-password")
async def change_password(
    password_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """修改密码"""
    old_password = password_data.get("old_password")
    new_password = password_data.get("new_password")

    if not old_password or not new_password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="旧密码和新密码都是必需的"
        )

    # 验证旧密码
    user_service = UserService(db)
    user = user_service.get(current_user.user_id)
    if not user or not verify_password(old_password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="旧密码错误"
        )

    # 更新密码
    from app.core.security import get_password_hash
    user.password_hash = get_password_hash(new_password)
    db.commit()

    return {"message": "密码修改成功"}


@router.post("/forgot-password")
async def forgot_password(
    email_data: dict,
    db: Session = Depends(get_db)
) -> Any:
    """忘记密码"""
    email = email_data.get("email")

    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱地址是必需的"
        )

    # 检查邮箱是否存在
    user_service = UserService(db)
    user = user_service.get_by_email(email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="邮箱不存在"
        )

    return {"message": "密码重置邮件已发送"}


@router.post("/verify-email")
async def verify_email(
    token: str,
    db: Session = Depends(get_db)
) -> Any:
    """邮箱验证"""
    return {"message": "邮箱验证成功"}
